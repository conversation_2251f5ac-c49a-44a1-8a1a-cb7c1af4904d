/**
 * 拣货波次相关常量配置
 */

// 波次状态枚举
export const WAVE_STATUS = {
  PENDING: 15,
  PICKING: 20,
  COMPLETED: 30,
  REVIEW_COMPLETED: 40
}

// 波次状态选项
export const WAVE_STATUS_OPTIONS = [
  { value: WAVE_STATUS.PENDING, label: '待拣货', color: '#E6A23C' },
  { value: WAVE_STATUS.PICKING, label: '拣货中', color: '#409EFF' },
  { value: WAVE_STATUS.COMPLETED, label: '已拣货', color: '#67C23A' }
]

// 状态标签类型映射
export const STATUS_TAG_TYPE_MAP = {
  [WAVE_STATUS.PENDING]: 'warning',
  [WAVE_STATUS.PICKING]: 'danger',
  [WAVE_STATUS.COMPLETED]: 'primary',
  [WAVE_STATUS.REVIEW_COMPLETED]: 'success'
}

// 基础表格列配置
export const BASE_TABLE_COLUMNS = [
  { prop: 'waveNo', label: '波次号', width: 160, fixed: 'left' },
  { prop: 'pickedQty', label: '已拣数量/应拣数量', width: 180 },
  { prop: 'pickingTypeName', label: '波次品种类型', width: 150 },
  { prop: 'orderCount', label: '订单数量', width: 80 },
]

// 不同状态下的额外列配置
export const STATUS_SPECIFIC_COLUMNS = {
  [WAVE_STATUS.PENDING]: [
    { prop: 'assigntor', label: '拣货员', width: 100 },
    { prop: 'printStatusName', label: '拣货单打印', width: 120 },
    { prop: 'mergeStatus', label: '面单拼接', width: 100 },
    { prop: 'createTime', label: '创建时间', minWidth: 180, showOverflowTooltip: true },
    { prop: 'status', label: '状态', width: 100, fixed: 'right' }
  ],
  [WAVE_STATUS.PICKING]: [
    { prop: 'assigntor', label: '拣货员', width: 100 },
    { prop: 'printStatusName', label: '拣货单打印', width: 120 },
    { prop: 'mergeStatus', label: '面单拼接', width: 100 },
    { prop: 'createTime', label: '创建时间', width: 180 },
    { prop: 'startTime', label: '提交时间', minWidth: 180, showOverflowTooltip: true },
    { prop: 'status', label: '状态', width: 100, fixed: 'right' }
  ],
  [WAVE_STATUS.COMPLETED]: [
    { prop: 'assigntor', label: '拣货员', width: 100 },
    { prop: 'printStatusName', label: '拣货单打印', width: 120 },
    { prop: 'mergeStatus', label: '面单拼接', width: 100 },
    { prop: 'sortingStatus', label: '二次分拣', width: 120 },
    { prop: 'reviewStatus', label: '复核', width: 180 },
    { prop: 'outboundStatus', label: '出库', width: 180 },
    { prop: 'createTime', label: '创建时间', width: 180 },
    { prop: 'endTime', label: '拣货完成时间', width: 180 },
    { prop: 'sortingTime', label: '二次分拣完成时间', width: 180 },
    { prop: 'reviewTime', label: '复核完成时间', width: 180 },
    { prop: 'outboundTime', label: '出库完成时间', width: 180 },
    { prop: 'status', label: '状态', width: 100, fixed: 'right' }
  ],
  // 全部状态显示所有列
  '': [
    { prop: 'outboundFinishedCount', label: '出库订单数量', width: 120 },
    { prop: 'assigntor', label: '拣货员', width: 100 },
    { prop: 'printStatusName', label: '拣货单打印', width: 120 },
    { prop: 'mergeStatus', label: '面单拼接', width: 100 },
    { prop: 'sortingStatus', label: '二次分拣', width: 120 },
    { prop: 'reviewStatus', label: '复核', width: 180 },
    { prop: 'outboundStatus', label: '出库', width: 180 },
    { prop: 'createTime', label: '创建时间', width: 180 },
    { prop: 'endTime', label: '拣货完成时间', width: 180 },
    { prop: 'sortingTime', label: '二次分拣完成时间', width: 180 },
    { prop: 'reviewTime', label: '复核完成时间', width: 180 },
    { prop: 'outboundTime', label: '出库完成时间', width: 180 },
    { prop: 'status', label: '状态', width: 100, fixed: 'right' }
  ]
}

// 行操作按钮配置
export const ROW_ACTIONS = {
  PICKING: {
    key: 'picking',
    label: '拣货',
    type: 'primary',
    link: true,
    visible: (row) => row.status === WAVE_STATUS.PENDING
  },
  PRINT_SUMMARY: {
    key: 'print_summary',
    label: '打印汇总拣货单',
    type: 'primary',
    link: true,
    visible: () => true // 始终显示
  },
  PRINT_SORTING: {
    key: 'print_sorting',
    label: '打印分拣拣货单',
    type: 'primary',
    link: true,
    visible: () => true // 始终显示
  },
  SECOND_SORTING: {
    key: 'second_sorting',
    label: '二次拣货',
    type: 'primary',
    link: true,
    visible: (row) => row.sortingStatus === 20
  },
  ASSIGN_PICKER: {
    key: 'assign_picker',
    label: '分配拣货员',
    type: 'primary',
    link: true,
    visible: (row) => row.status === WAVE_STATUS.PENDING
  },
  WAYBILL_MERGE: {
    key: 'waybill_merge',
    label: '面单拼接',
    type: 'primary',
    link: true,
    visible: (row) => row.mergeStatus === '0'
  },
  WAYBILL_VIEW: {
    key: 'waybill_view',
    label: '查看面单',
    type: 'primary',
    link: true,
    visible: (row) => row.mergeStatus === '2'
  },
  WAYBILL_PRINT: {
    key: 'waybill_print',
    label: '面单打印',
    type: 'primary',
    link: true,
    visible: (row) => row.mergeStatus === '2'
  },
}

// 批量操作按钮配置
export const BATCH_ACTIONS = {
  // ASSIGN: {
  //   key: 'batch-assign',
  //   label: '批量分配',
  //   type: 'primary',
  //   visible: (currentTab) => currentTab === '' || currentTab === WAVE_STATUS.PENDING
  // },
  ASSIGN_PICKER: {
    key: 'assign_picker',
    label: '分配拣货员',
    type: 'primary',
    visible: (currentTab) => currentTab === WAVE_STATUS.PENDING
  }
}

// 详情页操作按钮配置
export const DETAIL_ACTIONS = {
  ASSIGN: {
    key: 'assign',
    label: '分配拣货员',
    type: '',
    visible: (waveDetail) => waveDetail.status === WAVE_STATUS.PENDING
  },
  WAYBILL_MERGE: {
    key: 'waybill_merge',
    label: '面单拼接',
    type: '',
    visible: (row) => row.mergeStatus === '0'
  },
  QUICK_OUTBOUND: {
    key: 'quick_outbound',
    label: '快捷出库',
    type: '',
    visible: (row) => row.status === WAVE_STATUS.COMPLETED || row.status === WAVE_STATUS.REVIEW_COMPLETED
  },
  PRINT_SORTING: {
    key: 'print_sorting',
    label: '打印分拣拣货单',
    type: '',
    visible: (row) => true
  },
  PRINT_SUMMARY: {
    key: 'print_summary',
    label: '打印汇总拣货单',
    type: '',
    visible: (row) => true
  },
  PICKING: {
    key: 'picking',
    label: '拣货',
    type: '',
    visible: (waveDetail) => waveDetail.status === WAVE_STATUS.PENDING
  },
}


/**
 * 面单拼接文本
 */
export const MERGE_STATUS_TEXT = {
  "0": "未拼接",
  "2": "拼接成功"
}

/**
 * 二次分拣文本
 */
export const SORING_STATUS_TEXT = {
  15: "未完成",
  20: "已完成"
}

/**
 * 复核文本
 */
export const REVIEW_STATUS_TEXT = {
  0: "未完成",
  1: "已完成"
}

/**
 * 出库文本
 */
export const OUTBOUND_STATUS_TEXT = {
  0: "未出库",
  1: "已出库"
}

/**
 * Barcode类型查询
 */
export const BARCODE_TYPE_OPTIONS = [
  {
    value: 'barcode',
    label: 'Barcode',
    placeholder: '请输入Barcode'
  }
]

/**
 * 单号类型选项配置
 */
export const NUMBER_TYPE_OPTIONS = [
  {
    value: 'sourceNoList',
    label: '出库单号',
    placeholder: '请输入出库单号'
  },
  {
    value: 'referOrderNoList',
    label: '参考单号',
    placeholder: '请输入参考单号'
  },
  {
    value: 'waveNoList',
    label: '波次号',
    placeholder: '请输入波次号'
  },
  {
    value: 'expressNoList',
    label: '跟踪号',
    placeholder: '请输入跟踪号'
  }
]

/**
 * 出库单类型选项（静态配置）
 */
export const ORDER_TYPE_OPTIONS = [
  { value: '1', label: '小包出库单' },
  { value: '2', label: '大包出库单' },
  { value: '3', label: '其他出库单' }
]

/**
 * 波次品种类型选项（静态配置）
 */
export const PICKING_TYPE_OPTIONS = [
  { value: '1', label: '一单一件' },
  { value: '2', label: '一单多件' }
]

/**
 * 拣货员分配状态选项（静态配置）
 */
export const ASSIGN_PICKER_OPTIONS = [
  { value: '0', label: '全部' },
  { value: '1', label: '已分配' },
  { value: '2', label: '未分配' }
]

/**
 * 二次分拣状态选项（静态配置）
 */
export const SORTING_FLAG_OPTIONS = [
  { value: '0', label: '未完成' },
  { value: '1', label: '已完成' }
]

/**
 * 复核状态选项（静态配置）
 */
export const REVIEW_FLAG_OPTIONS = [
  { value: '0', label: '未完成' },
  { value: '1', label: '已完成' }
]

/**
 * 出库状态选项（静态配置）
 */
export const OUTBOUND_FLAG_OPTIONS = [
  { value: '0', label: '未完成' },
  { value: '1', label: '已完成' }
]

/**
 * 时间类型选项（静态配置）
 */
export const TIME_TYPE_OPTIONS = [
  { value: 'createTime', label: '创建时间' },
  { value: 'auditTime', label: '提交时间' },
  { value: 'pickTime', label: '拣货时间' },
  { value: 'sortingTime', label: '二次分拣完成时间' },
  { value: 'reviewTime', label: '复核完成时间' },
  { value: 'outboundTime', label: '出库完成时间' }
]
