<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  getWaveList,
  getWaveStatusSum,
  batchAssignPicker,
  batchStartPicking,
  batchFinishPicking,
  batchPrintPickingList,
  exportWaveData,
  getWaveDownload,
} from '@/api/outbound/wave'
import { useWaveConfig, useWaveSearchOptions } from '@/composables/useWaveConfig'
import WaveSearch from './components/WaveSearch.vue'
import WaveBatchActions from './components/WaveBatchActions.vue'
import WaveTable from './components/WaveTable.vue'

// 路由
const router = useRouter()

// 使用配置组合式函数
const { getTableColumns, getVisibleBatchActions } = useWaveConfig()

// 使用搜索选项管理
const { pickerOptions, logisticsCarrierOptions, logisticsChannelOptions, fetchAllOptions } =
  useWaveSearchOptions()

// 格式化日期为 Y-m-d HH:mm:ss 格式
const formatDateTime = (date, isEndTime = false) => {
  const d = new Date(date)
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  const time = isEndTime ? '23:59:59' : '00:00:00'
  return `${year}-${month}-${day} ${time}`
}

// 生成默认时间范围（最近3个月）
const getDefaultTimeRange = () => ({
  type: 'createTime',
  value: [
    new Date(Date.now() - 90 * 24 * 60 * 60 * 1000), // 3个月前
    new Date(), // 今天
  ],
})

// 查询条件
const queryParams = reactive({
  // 分页参数
  current: 1,
  size: 20,

  // 基础筛选参数
  status: '', // 状态（由标签页控制）
  whCode: 'CA', // 仓库代码
  logisticsCarrier: [], // 承运商（多选）
  logisticsChannel: [], // 物流渠道（多选）
  orderType: [], // 出库单类型（多选）
  pickingType: '', // 波次品种类型
  isAssignPicker: '', // 拣货员分配状态
  sortingFlag: '', // 二次分拣状态
  reviewFlag: '', // 复核状态
  outboundFlag: '', // 出库状态

  // 时间参数
  timeType: 'createTime', // 时间条件类型，默认创建时间
  startTime: '', // 开始时间
  endTime: '', // 结束时间
  timeRange: getDefaultTimeRange(), // 复合时间选择器

  // 搜索参数
  barcode: '', // Barcode（实际提交时使用）
  barcodeType: 'barcode', // Barcode类型
  barcodeValue: '', // Barcode值
  numberType: 'sourceNoList', // 单号类型
  numberValue: '', // 单号值

  // 单号列表（根据numberType和numberValue生成）
  waveNoList: [], // 波次号
  referOrderNoList: [], // 参考单号
  sourceNoList: [], // 出库单号
  expressNoList: [], // 跟踪号
})

// 表格加载状态
const loading = ref(false)

// 表格数据
const waveList = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)

// 标签页状态
const activeTab = ref('')
const statusSumOptions = ref([])

// 表格选中行
const selectedRows = ref([])

// 当前表格列配置
const currentTableColumns = computed(() => getTableColumns(activeTab.value))

// 构建查询参数
const buildQueryParams = (includeStatus = true) => {
  // 处理查询参数
  const params = {
    ...queryParams,
  }

  // 是否包含状态参数（状态统计接口可能不需要状态参数）
  if (includeStatus) {
    params.status = activeTab.value || queryParams.status
  }

  // 处理时间范围参数
  if (queryParams.timeRange?.type && queryParams.timeRange?.value) {
    params.timeType = queryParams.timeRange.type
    if (Array.isArray(queryParams.timeRange.value) && queryParams.timeRange.value.length === 2) {
      // 格式化开始时间为 Y-m-d 00:00:00
      params.startTime = formatDateTime(queryParams.timeRange.value[0], false)
      // 格式化结束时间为 Y-m-d 23:59:59
      params.endTime = formatDateTime(queryParams.timeRange.value[1], true)
    }
  }

  // 处理Barcode参数
  if (queryParams.barcodeType && queryParams.barcodeValue) {
    params.barcode = queryParams.barcodeValue
  }

  // 处理单号参数 - 根据单号类型设置对应的数组，只传递有值的参数
  if (queryParams.numberType && queryParams.numberValue) {
    const numberValues = queryParams.numberValue
      .split(/[,，\n]/)
      .map((v) => v.trim())
      .filter((v) => v)
    if (numberValues.length > 0) {
      params[queryParams.numberType] = numberValues
    }
  }

  // 处理多选参数 - 转换为逗号分隔的字符串
  if (Array.isArray(params.logisticsCarrier) && params.logisticsCarrier.length > 0) {
    params.logisticsCarrier = params.logisticsCarrier.join(',')
  }
  if (Array.isArray(params.logisticsChannel) && params.logisticsChannel.length > 0) {
    params.logisticsChannel = params.logisticsChannel.join(',')
  }
  if (Array.isArray(params.orderType) && params.orderType.length > 0) {
    params.orderType = params.orderType.join(',')
  }

  // 只对单号列表参数进行有值检查，有值才传递
  const numberListParams = ['waveNoList', 'referOrderNoList', 'sourceNoList', 'expressNoList']
  numberListParams.forEach((key) => {
    if (Array.isArray(params[key]) && params[key].length === 0) {
      delete params[key]
    }
  })

  // 删除不需要传递给后端的参数
  delete params.timeRange
  delete params.barcodeType
  delete params.barcodeValue
  delete params.numberType
  delete params.numberValue

  return params
}

// 获取波次列表
const fetchWaveList = async (showLoading = true) => {
  if (showLoading) {
    loading.value = true
  }

  try {
    const params = buildQueryParams(true)
    console.log(params)
    const response = await getWaveList(params)

    if (response && response.code === 200) {
      waveList.value = response.data.records || response.data.list || []
      total.value = response.data.total || 0
      currentPage.value = response.data.current || 1
      pageSize.value = response.data.size || 20
    }
  } catch (error) {
    console.error('获取波次列表失败', error)
    ElMessage.error('获取波次列表失败')
  } finally {
    if (showLoading) {
      loading.value = false
    }
  }
}

// 获取状态统计
const fetchStatusSum = async () => {
  try {
    // 构建查询参数，不包含状态参数（因为状态统计本身就是按状态分组的）
    const params = buildQueryParams(false)
    const response = await getWaveStatusSum(params)

    if (response && response.code === 200) {
      statusSumOptions.value = response.data || []
    }
  } catch (error) {
    console.error('获取状态统计失败', error)
  }
}

// 搜索
const handleQuery = () => {
  currentPage.value = 1
  queryParams.current = 1
  fetchWaveList()
  fetchStatusSum() // 刷新状态统计
}

// 重置搜索
const resetQuery = () => {
  Object.assign(queryParams, {
    // 分页参数
    current: 1,
    size: 20,

    // 基础筛选参数
    status: '', // 状态（由标签页控制）
    whCode: 'CA', // 仓库代码
    logisticsCarrier: [], // 承运商（多选）
    logisticsChannel: [], // 物流渠道（多选）
    orderType: [], // 出库单类型（多选）
    pickingType: '', // 波次品种类型
    isAssignPicker: '', // 拣货员分配状态
    sortingFlag: '', // 二次分拣状态
    reviewFlag: '', // 复核状态
    outboundFlag: '', // 出库状态

    // 时间参数
    timeType: 'createTime', // 时间条件类型，默认创建时间
    startTime: '', // 开始时间
    endTime: '', // 结束时间
    timeRange: getDefaultTimeRange(), // 复合时间选择器

    // 搜索参数
    barcode: '', // Barcode（实际提交时使用）
    barcodeType: 'barcode', // Barcode类型
    barcodeValue: '', // Barcode值
    numberType: '', // 单号类型
    numberValue: '', // 单号值

    // 单号列表（根据numberType和numberValue生成）
    waveNoList: [], // 波次号
    referOrderNoList: [], // 参考单号
    sourceNoList: [], // 出库单号
    expressNoList: [], // 跟踪号
  })

  handleQuery()
}

// 标签页切换
const handleTabChange = (tabName) => {
  activeTab.value = tabName
  handleQuery()
}

// 处理分页变化
const handlePaginationChange = ({ page, size }) => {
  currentPage.value = page
  pageSize.value = size
  queryParams.current = page
  queryParams.size = size
  fetchWaveList()
}

// 表格选择变化
const handleSelectionChange = (selection) => {
  selectedRows.value = selection
}

// 批量分配拣货员
const handleBatchAssign = async (data) => {
  try {
    loading.value = true
    const response = await batchAssignPicker(data)

    if (response && response.code === 200) {
      ElMessage.success('批量分配成功')
      fetchWaveList(false) // 不显示额外loading，因为外层已经管理
    }
  } catch (error) {
    console.error('批量分配失败', error)
    ElMessage.error('批量分配失败')
  } finally {
    loading.value = false
  }
}

// 批量开始拣货
const handleBatchStart = async (data) => {
  try {
    await ElMessageBox.confirm('确认批量开始拣货？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    loading.value = true
    const response = await batchStartPicking(data)

    if (response && response.code === 200) {
      ElMessage.success('批量开始拣货成功')
      fetchWaveList(false) // 不显示额外loading，因为外层已经管理
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量开始拣货失败', error)
      ElMessage.error('批量开始拣货失败')
    }
  } finally {
    loading.value = false
  }
}

// 批量完成拣货
const handleBatchFinish = async (data) => {
  try {
    await ElMessageBox.confirm('确认批量完成拣货？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    loading.value = true
    const response = await batchFinishPicking(data)

    if (response && response.code === 200) {
      ElMessage.success('批量完成拣货成功')
      fetchWaveList(false) // 不显示额外loading，因为外层已经管理
      fetchStatusSum() // 刷新状态统计
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量完成拣货失败', error)
      ElMessage.error('批量完成拣货失败')
    }
  } finally {
    loading.value = false
  }
}

// 批量打印拣货单
const handleBatchPrint = async (data) => {
  try {
    loading.value = true
    const response = await batchPrintPickingList(data)

    if (response && response.code === 200) {
      ElMessage.success('批量打印成功')
    }
  } catch (error) {
    console.error('批量打印失败', error)
    ElMessage.error('批量打印失败')
  } finally {
    loading.value = false
  }
}

// 导出数据
const handleExport = async () => {
  try {
    loading.value = true
    const response = await exportWaveData(queryParams)

    if (response && response.code === 200) {
      ElMessage.success('导出成功')
    }
  } catch (error) {
    console.error('导出失败', error)
    ElMessage.error('导出失败')
  } finally {
    loading.value = false
  }
}

// 单个分配拣货员
const handleAssignPicker = async (row) => {
  try {
    // TODO: 实现单个分配拣货员功能
    // 应该弹出分配对话框，选择拣货员后调用API
    // await assignSinglePicker({ waveId: row.id, pickerCode: selectedPicker })
    // fetchWaveList()
    ElMessage.info(`单个分配拣货员功能待实现 - 波次: ${row.waveNo}`)
  } catch (error) {
    console.error('分配拣货员失败', error)
    ElMessage.error('分配拣货员失败')
  }
}

// 单个开始拣货
const handleStartPicking = async (row) => {
  try {
    await ElMessageBox.confirm(`确认开始拣货波次：${row.waveNo}？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    ElMessage.success(`开始拣货：${row.waveNo}`)
    // 实际实现时应该调用开始拣货API并刷新列表
    // await startSinglePicking({ waveId: row.id })
    // fetchWaveList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('开始拣货失败', error)
      ElMessage.error('开始拣货失败')
    }
  }
}

// 单个完成拣货
const handleFinishPicking = async (row) => {
  try {
    await ElMessageBox.confirm(`确认完成拣货波次：${row.waveNo}？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    ElMessage.success(`完成拣货：${row.waveNo}`)
    // 实际实现时应该调用完成拣货API并刷新列表
    // await finishSinglePicking({ waveId: row.id })
    // fetchWaveList()
    // fetchStatusSum() // 刷新状态统计
  } catch (error) {
    if (error !== 'cancel') {
      console.error('完成拣货失败', error)
      ElMessage.error('完成拣货失败')
    }
  }
}

// 查看详情
const handleViewDetail = (row) => {
  // 跳转到详情页面
  router.push({
    path: `/outbound/wave/detail`,
    query: { waveNo: row.waveNo },
  })
}

// 打印汇总拣货单
const handleWaveDownload = async (row, printType) => {
  let iframe = null

  try {
    const params = {
      customerCode: '',
      printType: printType,
      waveNo: row.waveNo,
      whCode: row.whCode,
    }

    // 1. 调用接口获取PDF的Blob数据
    const response = await getWaveDownload(params)
    const blob = new Blob([response.data], { type: 'application/pdf' })
    // 检查 blob 是否为空，避免创建空的打印页
    if (blob.size === 0) {
      console.error('获取到的PDF文件内容为空')
      return
    }

    const blobUrl = URL.createObjectURL(blob)

    iframe = document.createElement('iframe')
    iframe.style.display = 'none'
    iframe.src = blobUrl

    const cleanup = () => {
      if (blobUrl) {
        URL.revokeObjectURL(blobUrl)
        blobUrl = null
      }
      if (iframe && iframe.parentNode) {
        document.body.removeChild(iframe)
        iframe = null
      }
    }

    iframe.onload = () => {
      try {
        iframe.contentWindow.focus()
        iframe.contentWindow.print()
        if (iframe.contentWindow.onafterprint !== undefined) {
          iframe.contentWindow.onafterprint = () => {
            cleanup()
          }
        } else {
          setTimeout(cleanup, 500)
        }
      } catch (e) {
        console.error('调用打印功能失败:', e)
        ElMessage.error('调用打印功能失败')
        cleanup()
      }
    }
    // 将 iframe 添加到 DOM 中
    document.body.appendChild(iframe)
  } catch (error) {
    console.error('获取或处理PDF文件时出错:', error)
    ElMessage.error('获取打印文件失败')
    // 如果出错时iframe已创建，也尝试清理
    if (iframe && iframe.parentNode) {
      document.body.removeChild(iframe)
    }
    if (blobUrl) {
      URL.revokeObjectURL(blobUrl)
    }
  }
}
// 初始化数据
onMounted(async () => {
  loading.value = true

  try {
    // 并行获取基础数据
    await Promise.all([
      fetchStatusSum(),
      fetchAllOptions(),
    ])
    await fetchWaveList(false)
  } catch (error) {
    console.error('初始化数据失败', error)
    ElMessage.error('初始化数据失败')
  } finally {
    loading.value = false
  }
})
</script>

<template>
  <div class="wave-container">
    <!-- 标签页区域 -->
    <el-tabs v-model="activeTab" @tab-change="handleTabChange" class="wave-tabs">
      <el-tab-pane label="全部" name="" />
      <template v-for="item in statusSumOptions" :key="item.status">
        <el-tab-pane
          v-if="item.status != 99"
          :label="`${item.statusName}(${item.statusSum})`"
          :name="String(item.status)"
        />
      </template>
    </el-tabs>

    <!-- 搜索区域 -->
    <WaveSearch
      :queryParams="queryParams"
      :logisticsCarrierOptions="logisticsCarrierOptions"
      :logisticsChannelOptions="logisticsChannelOptions"
      @search="handleQuery"
      @reset="resetQuery"
    />

    <!-- 批量操作区域 -->
    <WaveBatchActions
      v-if="getVisibleBatchActions(activeTab).length > 0"
      :selectedRows="selectedRows"
      :pickerOptions="pickerOptions"
      :currentTab="activeTab"
      @batch-assign="handleBatchAssign"
      @batch-start="handleBatchStart"
      @batch-finish="handleBatchFinish"
      @batch-print="handleBatchPrint"
      @export="handleExport"
    />

    <!-- 表格区域 -->
    <WaveTable
      :loading="loading"
      :waveList="waveList"
      :total="total"
      :currentPage="currentPage"
      :pageSize="pageSize"
      :tableColumns="currentTableColumns"
      @selection-change="handleSelectionChange"
      @pagination-change="handlePaginationChange"
      @assign-picker="handleAssignPicker"
      @start-picking="handleStartPicking"
      @finish-picking="handleFinishPicking"
      @view-detail="handleViewDetail"
      @wave-download="handleWaveDownload"
    />
  </div>
</template>

<style scoped>
.wave-container {
  padding: 20px;
  background-color: var(--el-bg-color-page);
  min-height: calc(100vh - 120px);
}

.wave-tabs {
  margin-bottom: 20px;
  background-color: var(--el-bg-color);
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

:deep(.el-tabs__header) {
  margin-bottom: 0;
  background-color: var(--el-bg-color);
  border-radius: 8px 8px 0 0;
}

:deep(.el-tabs__content) {
  display: none;
}

:deep(.el-tabs__nav-wrap) {
  background-color: var(--el-bg-color);
  border-radius: 8px 8px 0 0;
  border: 1px solid var(--el-border-color-light);
  border-bottom: none;
  padding: 0 20px;
}

:deep(.el-tabs__nav-scroll) {
  padding: 8px 10px;
}

:deep(.el-tabs__active-bar) {
  background-color: var(--el-color-primary);
  height: 3px;
}

:deep(.el-tabs__item) {
  color: var(--el-text-color-regular);
  font-weight: 500;
  padding: 0 20px;
  height: 40px;
  line-height: 40px;
  border-radius: 4px;
  margin: 0 4px;
  transition: all 0.3s ease;
}

.wave-tabs :deep(.el-tabs__item:nth-child(2)) {
  padding-left: 20px;
}

.wave-tabs :deep(.el-tabs__item:last-child) {
  padding-right: 20px;
}

:deep(.el-tabs__item:hover) {
  color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
}

:deep(.el-tabs__item.is-active) {
  color: var(--el-color-primary);
  font-weight: 600;
  background-color: var(--el-color-primary-light-9);
}
</style>
