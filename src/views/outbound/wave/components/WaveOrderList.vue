<script setup>
import { ref, defineProps, defineEmits, onMounted } from 'vue'
import { getWaveOrderList } from '@/api/outbound/wave'
import { ElMessage } from 'element-plus'

const props = defineProps({
  waveNo: {
    type: String,
    required: true,
  },
})

const emit = defineEmits(['view-order'])

// 表格数据
const loading = ref(false)
const orderList = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)

// 搜索参数
const productSku = ref('')

// 获取订单列表
const fetchOrderList = async () => {
  loading.value = true

  try {
    const params = {
      waveNo: props.waveNo,
      whCode: 'CA',
      current: currentPage.value,
      size: pageSize.value,
    }

    // 如果有 SKU 搜索条件，添加到参数中
    if (productSku.value) {
      params.productSku = productSku.value
    }

    const response = await getWaveOrderList(params)

    if (response && response.code === 200) {
      orderList.value = response.data.records || []
      total.value = response.data.total || 0
    }
  } catch (error) {
    console.error('获取订单列表失败', error)
    ElMessage.error('获取订单列表失败')
  } finally {
    loading.value = false
  }
}

// 处理分页变化
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  fetchOrderList()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  fetchOrderList()
}

// 查看订单详情
const handleViewOrder = (row) => {
  emit('view-order', row)
}

// 搜索功能
const handleSearch = () => {
  currentPage.value = 1
  fetchOrderList()
}

// 清空搜索
const handleClearSearch = () => {
  productSku.value = ''
  currentPage.value = 1
  fetchOrderList()
}

// 初始化
onMounted(() => {
  fetchOrderList()
})
</script>

<template>
  <div class="order-list-container">
    <div class="order-list-search">
      <el-input
        v-model="productSku"
        style="width: 240px"
        placeholder="搜索SKU，按 Enter 搜索"
        clearable
        @keyup.enter="handleSearch"
        @clear="handleClearSearch"
      />
    </div>

    <el-table :data="orderList" :loading="loading" stripe style="width: 100%">
      <el-table-column prop="sourceNo" label="出库单号" width="180" fixed="left">
        <template #default="scope">
          <el-link type="primary" @click="handleViewOrder(scope.row)">
            {{ scope.row.sourceNo }}
          </el-link>
        </template>
      </el-table-column>

      <el-table-column prop="customerName" label="客户" width="140" />

      <el-table-column label="SKU * 数量(单箱)" width="180">
        <template #default="scope">
          <div v-if="scope.row.productList && scope.row.productList.length > 0">
            <!-- 单条记录直接展示 -->
            <span v-if="scope.row.productList.length === 1">
              {{ scope.row.productList[0].productSku }} * {{ scope.row.productList[0].qty }}
            </span>
            <!-- 多条记录显示数量，鼠标悬浮展示详情 -->
            <el-tooltip v-else placement="top" effect="dark">
              <template #content>
                <div class="product-tooltip">
                  <div
                    v-for="(product, index) in scope.row.productList"
                    :key="index"
                    class="product-item"
                  >
                    {{ product.productSku }} * {{ product.qty }}
                  </div>
                </div>
              </template>
              <span class="multiple-products"> 多个({{ scope.row.productList.length }}条) </span>
            </el-tooltip>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>

      <el-table-column prop="varietyTypeName" label="订单类型" width="120" />

      <el-table-column prop="deliveryStatusName" label="状态" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.deliveryStatus === 100 ? 'success' : 'warning'">
            {{ scope.row.deliveryStatusName }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column
        prop="logisticsChannel"
        label="物流渠道"
        min-width="180"
        show-overflow-tooltip
      />

      <el-table-column prop="expressNo" label="物流跟踪号" width="120" show-overflow-tooltip />

      <el-table-column prop="createTime" label="创建时间" width="160" show-overflow-tooltip />

      <el-table-column prop="reviewTime" label="复核时间" width="160" show-overflow-tooltip />

      <el-table-column prop="outboundTime" label="出库时间" width="160" show-overflow-tooltip />

      <el-table-column label="操作" width="100" fixed="right">
        <template #default="scope">
          <el-button v-if="scope.row.deliveryStatus === 15 || scope.row.deliveryStatus === 30" type="primary" link @click="handleViewOrder(scope.row)"> 标记异常 </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        :current-page="currentPage"
        :page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<style scoped>
.order-list-container {
  padding: 0;
}

.order-list-search {
  display: flex;
  justify-content: flex-end;
}

.order-list-search {
  margin-bottom: 16px;
  display: flex;
  align-items: center;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

:deep(.el-table) {
  border-radius: 8px;
}

:deep(.el-table__header) {
  background-color: var(--el-bg-color-page);
}

:deep(.el-table th) {
  background-color: var(--el-bg-color-page) !important;
  color: var(--el-text-color-primary) !important;
  font-weight: 600;
}

:deep(.el-table td) {
  padding: 12px 0;
}

.multiple-products {
  color: var(--el-color-primary);
  cursor: pointer;
  font-weight: 500;
}

.product-tooltip {
  max-width: 300px;
}

.product-item {
  padding: 2px 0;
  font-size: 12px;
  line-height: 1.4;
}

.product-item:not(:last-child) {
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  margin-bottom: 4px;
  padding-bottom: 4px;
}
</style>
