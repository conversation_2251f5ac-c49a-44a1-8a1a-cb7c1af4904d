<script setup>
import { ref, onMounted } from 'vue'
import { backDetail, back } from '@/api/outbound/intercept'
import { ElMessage } from 'element-plus'
import { DocumentCopy, Search } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { useTabsStore } from '@/store/modules/tabs'

const router = useRouter()

const tabsStore = useTabsStore()

const interceptDetail = ref([])

const interceptNo = ref(null)
const customerCode = ref(null)
const whCode = ref(null)
const loading = ref(false)

const interceptInfo = async () => {
  loading.value = true
  try {
    // 构建查询参数
    const params = {
      interceptNo: interceptNo.value,
      customerCode: customerCode.value,
      whCode: whCode.value,
    }

    const response = await backDetail(params)

    if (response && response.data) {
      interceptDetail.value = response.data || []
    }
  } catch (error) {
    console.error('获取列表失败', error)
    ElMessage.error('获取列表失败')
  } finally {
    loading.value = false
  }
}

//复制
const copy = async (val) => {
  await navigator.clipboard.writeText(val)
  ElMessage.success('复制成功')
}

const handleSub = async () => {
  try {
    // 构建查询参数
    const params = {
      interceptNo: interceptNo.value,
      customerCode: customerCode.value,
      whCode: whCode.value,
      operateType: 0,
      sourceNo: interceptDetail.value.sourceNo,
      tenantCode: interceptDetail.value.tenantCode,
      skuList: interceptDetail.value.recommendSkuList,
    }

    const response = await back(params)

    if (response && response.code == 200) {
      ElMessage.success('操作成功')
      closeTab()
      router.push('/outbound/intercept/listing')
    }
  } catch (error) {
    console.error('获取列表失败', error)
    ElMessage.error('获取列表失败')
  } finally {
    loading.value = false
  }
}

const handleBack = () => {
  closeTab()
  router.go(-1)
}

const closeTab = () => {
  tabsStore.tabs.map((tab) => {
    if (tab.path == '/outbound/intercept/backDetail') {
      tabsStore.removeTab(tab.name)
    }
  })
}

const handelCellNo = () => {
  alert('选库位')
}

// 初始化
onMounted(() => {
  interceptNo.value = router.currentRoute.value.query.interceptNo
  customerCode.value = router.currentRoute.value.query.customerCode
  whCode.value = router.currentRoute.value.query.whCode

  interceptInfo()
})
</script>

<template>
  <div>
    <el-form label-width="100px" label-position="left">
      <el-card class="profile-card" shadow="hover" v-loading="loading">
        <el-row>
          <el-col :span="12">
            <span style="margin-left: 0px">
              <label style="font-size: 24px; font-weight: bold">{{
                interceptDetail.interceptNo
              }}</label>
              <el-link
                type="primary"
                style="margin-left: 5px; margin-bottom: 15px"
                :icon="DocumentCopy"
                @click="copy(interceptDetail.interceptNo)"
              >
              </el-link>
            </span>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="客户" prop="">
              <!-- <span>客户</span> -->
              <span class="type-name" v-if="interceptDetail.customerName">
                {{ interceptDetail.customerName }} ({{ interceptDetail.customerCode }})
              </span>
              <span class="type-name" v-else>-</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="物流渠道" prop="">
              <span class="type-name" v-if="interceptDetail.logisticsChannelName">
                {{ interceptDetail.logisticsChannelName }} ({{ interceptDetail.logisticsChannel }})
              </span>
              <span class="type-name" v-else>-</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="物流跟踪号" prop="">
              <span class="type-name" v-if="interceptDetail.expressNo">
                {{ interceptDetail.expressNo }}
              </span>
              <span class="type-name" v-else>-</span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      <el-card style="margin-top: 10px">
        <div class="app-container">
          <span style="font-size: 20px; font-weight: bold; color: #0071e3">丨</span>
          <span style="font-size: 16px; font-weight: bolder">上架明细</span>
          <br />
          <el-table :data="interceptDetail.recommendSkuList" style="width: 100%; margin-top: 20px">
            <el-table-column label="SKU" prop="productSku" width="" />
            <el-table-column label="产品明层" width="" prop="productName" />
            <el-table-column label="应上架数量" prop="directQty" width="" />
            <el-table-column label="实际上架数量" prop="directQty" width="" />
            <el-table-column label="库位" prop="cellNo" width="">
              <template #default="scope">
                <el-input-tag :value="scope.row.cellNo" readonly>
                  <template #suffix>
                    <el-icon @click="handelCellNo"><Search /></el-icon>
                  </template>
                </el-input-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" prop="" width="120px;" fixed="right" />
          </el-table>
        </div>
      </el-card>
    </el-form>

    <div class="footer">
      <el-button style="width: 85px; height: 45px" @click="handleBack">取消</el-button>
      <el-button style="width: 85px; height: 45px" type="primary" @click="handleSub"
        >确认上架</el-button
      >
    </div>
  </div>
</template>

<style>
.form-item-text-align {
  text-align: left;
}

.footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  margin-left: 0px;
  text-align: center;
}
</style>
