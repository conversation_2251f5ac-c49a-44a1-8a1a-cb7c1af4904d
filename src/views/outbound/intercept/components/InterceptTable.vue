<script setup>
import { ref, defineProps, defineEmits } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { DocumentCopy } from '@element-plus/icons-vue'

// 路由相关
const router = useRouter()

const props = defineProps({
  loading: {
    type: Boolean,
    default: false,
  },
  interceptList: {
    type: Array,
    required: true,
  },
  total: {
    type: Number,
    default: 0,
  },
  currentPage: {
    type: Number,
    default: 1,
  },
  pageSize: {
    type: Number,
    default: 10,
  },
  statusType: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['pagination-change', 'selection-change', 'back', 'fail'])

// 表格选中行
const selectedRows = ref([])
const handleSelectionChange = (selection) => {
  selectedRows.value = selection
  emit('selection-change', selection)
}

// 处理页码或每页条数变化
const handleSizeChange = (size) => {
  emit('pagination-change', { page: props.currentPage, size })
}

const handleCurrentChange = (page) => {
  emit('pagination-change', { page, size: props.pageSize })
}

//回库上架
const handleBack = (row) => {
  emit('back', row)
}

//标记失败
const handleFail = (row) => {
  emit('fail', row)
}

//复制
const handleCopy = async (val) => {
  await navigator.clipboard.writeText(val)
  ElMessage.success('复制成功')
}

const handleDetail = (row) => {
  router.push({
    path: '/outbound/detail',
    query: {
      deliveryNo: row.deliveryNo,
      customerCode: row.customerCode,
      whCode: row.whCode,
      sourceNo: row.sourceNo,
    },
  })
}
</script>

<template>
  <el-card class="table-card" shadow="hover">
    <template #header>
      <div class="card-header">
        <!--        <span>用户列表</span>-->
      </div>
    </template>

    <div v-if="interceptList.length === 0" class="empty-data">
      <p>暂无数据</p>
    </div>

    <!-- 表格 -->
    <el-table v-loading="loading" :data="interceptList" @selection-change="handleSelectionChange">
      <el-table-column label="出库单号" prop="sourceNo" width="180" fixed="left">
        <template #default="scope">
          <el-link type="primary" v-if="scope.row.sourceNo" @click="() => handleDetail(scope.row)">
            {{ scope.row.sourceNo }}
          </el-link>
          <el-link
            type="primary"
            style="margin-left: 5px; margin-bottom: 8px"
            :icon="DocumentCopy"
            @click="handleCopy(scope.row.sourceNo)"
          >
          </el-link>
        </template>
      </el-table-column>
      <el-table-column
        label="参考单号"
        prop="referOrderNo"
        width="200px;"
        align="center"
        show-overflow-tooltip
      >
        <template #default="scope">
          {{ scope.row.referOrderNo }}
          <el-link
            type="primary"
            v-if="scope.row.referOrderNo"
            style="margin-left: 5px; margin-bottom: 8px"
            :icon="DocumentCopy"
            @click="handleCopy(scope.row.referOrderNo)"
          >
          </el-link>
        </template>
      </el-table-column>
      <el-table-column
        label="平台单号"
        prop="platformOrderNo"
        width="200px;"
        align="center"
        show-overflow-tooltip
      >
        <template #default="scope">
          {{ scope.row.platformOrderNo }}
          <el-link
            type="primary"
            v-if="scope.row.platformOrderNo"
            style="margin-left: 5px; margin-bottom: 8px"
            :icon="DocumentCopy"
            @click="handleCopy(scope.row.platformOrderNo)"
          >
          </el-link>
        </template>
      </el-table-column>
      <el-table-column
        label="SKU * 数量"
        prop=""
        width="200px;"
        align="center"
        show-overflow-tooltip
      >
        <template #default="scope">
          <span v-if="scope.row.productList.length < 2" v-for="pro in scope.row.productList"
            >{{ pro.productSku }} * {{ pro.qty }}
          </span>
          <el-popover v-else effect="light" trigger="hover" placement="top" width="auto">
            <template #default>
              <el-table :data="scope.row.productList">
                <el-table-column label="SKU" prop="productSku" width="150" />
                <el-table-column label="数量" prop="qty" width="100" />
              </el-table>
            </template>
            <template #reference>
              <el-tag>多个({{ scope.row.productList.length }})</el-tag>
            </template>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column label="客户" align="center" width="200px" show-overflow-tooltip>
        <template #default="scope">
          {{ scope.row.customerName }} ({{ scope.row.customerCode }})
        </template>
      </el-table-column>
      <el-table-column
        label="物流渠道"
        prop="logisticsChannelName"
        width="180px;"
        align="center"
        show-overflow-tooltip
      >
        <template #default="scope">
          {{ scope.row.logisticsChannelName }} ({{ scope.row.logisticsChannel }})
        </template>
      </el-table-column>
      <el-table-column
        label="物流跟踪号"
        prop="expressNo"
        width="230px;"
        align="center"
        show-overflow-tooltip
      >
        <template #default="scope">
          {{ scope.row.expressNo }}
          <el-link
            type="primary"
            v-if="scope.row.expressNo"
            style="margin-left: 5px; margin-bottom: 8px"
            :icon="DocumentCopy"
            @click="handleCopy(scope.row.expressNo)"
          >
          </el-link>
        </template>
      </el-table-column>
      <el-table-column label="发起拦截时间" prop="createTime" width="180" align="center" />
      <!--      <el-table-column label="出库状态" prop="outboundStatusName" width="100" align="center" >-->
      <!--        <template #default="scope">-->
      <!--          <el-tag v-if="scope.row.outboundStatus == 99" type="info">{{ scope.row.outboundStatusName }}</el-tag>-->
      <!--          <el-tag v-else >{{ scope.row.outboundStatusName }}</el-tag>-->
      <!--        </template>-->
      <!--      </el-table-column>-->

      <el-table-column
        label="确认拦截时间"
        prop=""
        width="100px;"
        v-if="statusType == 15 || statusType == 20"
      >
        <template #default="scope">
          <el-tag>{{ scope.row.interceptTime }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="出库单状态"
        prop="statusName"
        fixed="right"
        width="100px;"
        v-if="statusType != ''"
      >
        <template #default="scope">
          <el-tag v-if="scope.row.outboundStatus == 99" type="info">{{
            scope.row.outboundStatusName
          }}</el-tag>
          <el-tag v-else-if="scope.row.outboundStatus == 100" type="success">{{
            scope.row.outboundStatusName
          }}</el-tag>
          <el-tag v-else type="warning">{{ scope.row.outboundStatusName }}</el-tag>
        </template>
      </el-table-column>

      <el-table-column
        label="状态"
        prop="statusName"
        fixed="right"
        width="100px;"
        v-if="statusType == ''"
      >
        <template #default="scope">
          <el-tag v-if="scope.row.status == 10">{{ scope.row.statusName }}</el-tag>
          <el-tag v-else-if="scope.row.status == 15" type="success">{{
            scope.row.statusName
          }}</el-tag>
          <el-tag v-else-if="scope.row.status == 20" type="info">{{ scope.row.statusName }}</el-tag>
          <el-tag v-else>{{ scope.row.statusName }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        fixed="right"
        width="180"
        v-if="statusType != 15 && statusType != 20"
        align="center"
      >
        <template #default="scope">
          <span v-if="scope.row.status == 10">
            <el-button link type="primary" @click="handleBack(scope.row)"> 回库上架 </el-button>
            <el-button link type="primary" @click="handleFail(scope.row)"> 标记失败 </el-button>
          </span>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <div class="pagination-container">
      <el-pagination
        background
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[10, 20, 50, 100]"
        :current-page="currentPage"
        :page-size="pageSize"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-card>
</template>

<style scoped>
.table-card {
  border-radius: var(--card-radius);
  overflow: hidden;
  box-shadow: var(--card-shadow);
  border: none;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 18px;
  font-weight: 500;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}
</style>
