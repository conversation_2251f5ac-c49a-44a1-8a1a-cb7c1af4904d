<script setup>
// 1. 导入 (Imports)
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { listing, options, statusSum, consumers, channels, back } from '@/api/outbound/intercept'
import { useRouter } from 'vue-router'
import InterceptSearch from './components/InterceptSearch.vue'
import InterceptTable from './components/InterceptTable.vue'

// 路由相关
const router = useRouter()

// 2. Props & Emits 定义 (此组件无props和emits)

// 3. 响应式状态 (Reactive State)
// 查询条件
const queryParams = reactive({
  current: 1,
  customerCodes: '',
  logisticsCarrier: '',
  logisticsChannel: '',
  multiKeyword: '',
  outboundType: '',
  searchType: 'barcode',
  size: 20,
  orderNoType: 'sourceNo',
  sourceNoVal: '',
})

const dateParams = reactive({
  dateType: 'createTime', // 默认日期类型为创建时间
  dateRange: [],
})

// 表格加载状态
const loading = ref(false)

// 表格数据
const interceptList = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)

const statusType = ref(10) //
const statusSumOptions = ref([])

// 日期类型选项
const dateTypeOptions = ref([])
const orderNoOptions = ref([])

// options
const consumerList = ref([])
const channelList = ref([])

// 表格选中行
const selectedRows = ref([])
const handleSelectionChange = (selection) => {
  selectedRows.value = selection
}

// 搜索
const handleQuery = async () => {
  loading.value = true
  try {
    // 构建查询参数
    const params = {
      current: currentPage.value,
      size: pageSize.value,
      status: 10,
    }

    // 处理日期范围
    if (dateParams.dateRange && dateParams.dateRange.length === 2) {
      const [beginDate, endDate] = dateParams.dateRange
      params.startTime = beginDate
      params.endTime = endDate
      params.timeType = dateParams.dateType
    } else {
      params.timeType = ''
    }

    if (queryParams.sourceNoVal && queryParams.orderNoType) {
      params.orderNoType = queryParams.orderNoType
      var parKey = ''
      orderNoOptions.value.map((item, index) => {
        if (item.key == queryParams.orderNoType) {
          parKey = item.parKey
          return
        }
      })
      if (parKey) {
        params[parKey] = queryParams.sourceNoVal
      }
    }

    params.customerCodes = queryParams.customerCodes
    params.logisticsChannel = queryParams.logisticsChannel

    //重新计算分类数量
    fetchStatusSum(params)

    params.status = statusType.value

    const response = await listing(params)

    if (response && response.data.records.length > 0) {
      interceptList.value = response.data.records || []
      total.value = response.data.total || 0
      currentPage.value = response.data.current || 1
      pageSize.value = response.data.size || 20
    } else {
      interceptList.value = []
      total.value = 0
      if (currentPage.value > 1) {
        currentPage.value = 1
        handleQuery()
      }
    }
  } catch (error) {
    console.error('获取列表失败', error)
    ElMessage.error('获取列表失败')
  } finally {
    loading.value = false
  }
}

// 重置搜索
const resetQuery = () => {
  ;(queryParams.current = 1),
    (queryParams.customerCodes = ''),
    (queryParams.logisticsCarrier = ''),
    (queryParams.logisticsChannel = ''),
    (queryParams.multiKeyword = ''),
    (queryParams.outboundType = ''),
    (queryParams.searchType = 'barcode'),
    (queryParams.size = 20),
    (queryParams.orderNoType = 'sourceNo'),
    (dateParams.dateRange = []),
    (currentPage.value = 1)
  pageSize.value = 20

  handleQuery()
}

// 处理分页变化
const handlePaginationChange = ({ page, size }) => {
  currentPage.value = page
  pageSize.value = size
  handleQuery()
}

// option consumers
const fetchConsumers = async () => {
  loading.value = true

  try {
    const response = await consumers()

    if (response && response.data) {
      consumerList.value = response.data || []
    }
  } catch (error) {
    console.error('获取客户失败', error)
    ElMessage.error('获取客户失败')
  } finally {
    loading.value = false
  }
}

// option channels
const fetchChannels = async () => {
  loading.value = true

  try {
    const response = await channels()

    if (response && response.data) {
      channelList.value = response.data || []
    }
  } catch (error) {
    console.error('获取渠道失败', error)
    ElMessage.error('获取渠道失败')
  } finally {
    loading.value = false
  }
}

const fetchOptions = async () => {
  loading.value = true

  try {
    const response = await options()

    if (response && response.data) {
      dateTypeOptions.value = response.data.interceptDateType || []
      orderNoOptions.value = response.data.interceptOrderNo || []
    }
  } catch (error) {
    console.error('获取数据失败', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

const fetchStatusSum = async (queryParams = null) => {
  loading.value = true

  try {
    const response = await statusSum(queryParams)

    if (response && response.data) {
      statusSumOptions.value = response.data || []
    }
  } catch (error) {
    console.error('获取数据失败', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

const whCode = ref('CA')
//回库上架
const handleBack = (row) => {
  router.push({
    path: '/outbound/intercept/backDetail',
    query: {
      interceptNo: row.interceptNo,
      customerCode: row.customerCode,
      whCode: whCode.value,
    },
  })
}

//标记失败
const handleFail = async (row) => {
  try {
    ElMessageBox.confirm('标记拦截失败的出库单将不再支持操作，确认继续吗?', '标记失败', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(async () => {
      // 构建查询参数
      const params = {
        interceptNo: row.interceptNo,
        customerCode: row.customerCode,
        whCode: whCode.value,
        operateType: 1,
        sourceNo: row.sourceNo,
        tenantCode: row.tenantCode,
      }

      const response = await back(params)
      if (response && response.code == 200) {
        ElMessage.success('操作成功')
        handleQuery()
      }
    })
  } catch (error) {
    ElMessage.error('操作失败')
  } finally {
  }
}

// 初始化
onMounted(() => {
  handleQuery()

  fetchConsumers()
  fetchChannels()
  fetchOptions()
})
</script>

<template>
  <div class="user-container">
    <!-- 搜索区域 -->
    <InterceptSearch
      :queryParams="queryParams"
      :dateTypeOptions="dateTypeOptions"
      :orderNoOptions="orderNoOptions"
      :consumerList="consumerList"
      :channelList="channelList"
      :dateParams="dateParams"
      @search="handleQuery"
      @reset="resetQuery"
    />

    <el-tabs v-model="statusType" @tab-change="handleQuery" class="warehouse-tabs">
      <el-tab-pane label="全部" name="" />
      <el-tab-pane
        v-for="item in statusSumOptions"
        :key="item.status"
        :label="item.statusNameCount"
        :name="item.status"
      />
      <!-- {{ item.statusName }} + ({{item.statusSum}}) -->
    </el-tabs>
    <!-- 表格 -->
    <InterceptTable
      :loading="loading"
      :interceptList="interceptList"
      :total="total"
      :currentPage="currentPage"
      :pageSize="pageSize"
      @pagination-change="handlePaginationChange"
      :statusType="statusType"
      @back="handleBack"
      @fail="handleFail"
      @selection-change="handleSelectionChange"
    />
  </div>
</template>

<style scoped>
.user-container {
  padding: 20px;
}
</style>
