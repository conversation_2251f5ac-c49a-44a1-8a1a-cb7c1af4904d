<script setup>
import { defineProps, defineEmits } from 'vue'
import { Warning } from '@element-plus/icons-vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  queryTips: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['success', 'cancel', 'intercept'])

// 关闭对话框
const handleClose = () => {
  emit('cancel')
}

// 前往处理截单
const toIntercept = () => {
  emit('intercept')
}

// 确定
const submitForm = async () => {
  try {
    emit('success')
  } catch (error) {
    console.error('操作失败', error)
  }
}
</script>

<template>
  <el-dialog :model-value="visible" width="500px">
    <div class="app-container">
      <div style="text-align: left">
        <span style="">
          <el-icon><Warning /></el-icon>
          提示
        </span>
      </div>

      <div style="text-align: center; margin-top: 30px">
        <span style=""> 该波次中存在单据为截单状态，确认仍要继续复核吗？ </span>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" plain @click="toIntercept">前往处理截单</el-button>
        <el-button type="primary" @click="submitForm">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}
</style>
