<script setup>
import { defineProps } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

const props = defineProps({
  loading: {
    type: Boolean,
    default: false,
  },
  orderList: {
    type: Array,
    default: () => [],
  },
  orderData: {
    type: Array,
    default: () => [],
  },
  moreConsumer: {
    type: Array,
    default: () => [],
  },
  morePiece: {
    type: Array,
    default: () => [],
  },
  orderListTips: {
    type: String,
    default: '',
  },
})

//一单多件 - 打印面单
const printMoreOrder = () => {
  alert('一单多件 - 打印面单')
}
</script>

<template>
  <!-- 一单一件 -->
  <div class="profile-container" v-if="orderList.length > 0" v-loading="loading"></div>
  <!-- 一单多件 -->
  <div
    class="profile-container"
    v-else-if="moreConsumer.outBoundList && morePiece.skuList"
    v-loading="loading"
  >
    <el-form label-width="100px" label-position="left">
      <el-card class="profile-card" shadow="hover">
        <div class="app-container">
          <span style="font-size: 16px" v-if="morePiece">
            <label style="color: green">{{ morePiece.scanOrderSkuQty }}</label>
            <label> / {{ morePiece.orderSkuAllQty }}</label>
            <label style="color: #9ca3af; margin-left: 8px">已扫 / 全部</label>
          </span>
          <el-row
            v-for="item in moreConsumer.outBoundList"
            style="margin-top: 20px; font-size: 14px"
          >
            <el-col :span="8">
              <label style="color: #9ca3af">出库单号</label><br />
              <label style="color: black">{{ item.sourceNo }}</label>
            </el-col>
            <el-col :span="8">
              <label style="color: #9ca3af">物流跟踪号</label><br />
              <label style="color: black">{{ item.expressNo }}</label>
            </el-col>
            <el-col :span="8">
              <label style="color: #9ca3af">客户</label><br />
              <label style="color: black">{{ item.customerName }}({{ item.customerCode }})</label>
            </el-col>
          </el-row>

          <!-- <el-table :data="moreConsumer.outBoundList" style="width: 100%;margin-top: 20px;">
            <el-table-column label="出库单号">
              <template #default="scope">
                <span style="font-size: 14px;">{{ scope.row.sourceNo }}</span>
              </template>
            </el-table-column>
            <el-table-column label="物流跟踪号">
              <template #default="scope">
                <span style="font-size: 14px;">{{ scope.row.expressNo }}</span>
              </template>
            </el-table-column>
            <el-table-column label="客户" >
              <template #default="scope">
                <span style="font-size: 14px;">{{ scope.row.customerName }}({{ scope.row.customerCode }})</span>
              </template>
            </el-table-column>
          </el-table> -->
        </div>
        <div class="app-container">
          <el-table :data="morePiece.skuList" style="width: 100%; margin-top: 30px">
            <el-table-column label="图片" width="120px;">
              <template #default="scope">
                <el-image
                  style="width: 50px; height: 50px"
                  :src="scope.row.productPicVO.mainUrl"
                  fit="cover"
                />
              </template>
            </el-table-column>
            <el-table-column label="SKU/产品条码/产品名称" prop="" width="250px;">
              <template #default="scope">
                <span style="font-size: 20px; font-weight: bold">{{ scope.row.productSku }}</span
                ><br />
                <span>{{ scope.row.barcode }}</span
                ><br />
                <span>{{ scope.row.productName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="是否需要包材" width="150px;">
              <template #default="scope">
                <span v-if="scope.row.needPack == 0">
                  <el-tag class="ml-2" type="warning">否</el-tag>
                </span>
                <span v-else>
                  <el-tag class="ml-2" type="info">是</el-tag>
                </span>
              </template>
            </el-table-column>
            <el-table-column label="推荐包材编码" width="150px;">
              <template #default="scope">
                <span v-if="scope.row.recommendedPackMaterial">
                  {{ scope.row.recommendedPackMaterial.code }}
                </span>
              </template>
            </el-table-column>
            <el-table-column label="推荐包材名称" width="150px;">
              <template #default="scope">
                <span v-if="scope.row.recommendedPackMaterial">
                  {{ scope.row.recommendedPackMaterial.name }}
                </span>
              </template>
            </el-table-column>
            <el-table-column label="已扫/总数量" width="150px;">
              <template #default="scope">
                <span style="color: green">
                  {{ scope.row.scanQty }}
                </span>
                <span v-if="scope.row.allQty"> / {{ scope.row.allQty }} </span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>
    </el-form>
    <div style="float: right; margin-top: 10px">
      <el-button @click="printMoreOrder" style="width: 85px; height: 35px; font-size: 16px"
        >打印面单</el-button
      >
    </div>
  </div>

  <div class="defaultTips" v-else>
    <img src="/scan.svg" />
    <label>
      {{ props.orderListTips }}
    </label>
  </div>
</template>

<style>
.defaultTips {
  text-align: center;
  color: #606266;
  display: flex;
  justify-content: center; /* 水平居中 */
  align-items: center; /* 垂直居中 */
  height: 100vh;
}
</style>
