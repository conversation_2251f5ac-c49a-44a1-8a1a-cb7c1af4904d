<script setup>
import { defineProps, defineEmits } from 'vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: '',
  },
  settingData: {
    type: Object,
    default: () => ({}),
  },
})

const emit = defineEmits(['success', 'cancel'])

const weightCheck = (val) => {
  if (!val) {
    props.settingData.weighType = ''
  } else {
    props.settingData.weighType = '1'
  }
}

// 关闭对话框
const handleClose = () => {
  emit('cancel')
}

// 确定
const submitForm = async () => {
  try {
    // 通知父组件操作成功
    emit('success', props.settingData)
  } catch (error) {
    console.error('操作失败', error)
  }
}
</script>

<template>
  <el-dialog :title="title" :model-value="visible" width="400px">
    <br />
    <el-form label-width="0px" :model="settingData">
      <el-form-item label="" prop="">
        <el-checkbox label="复核后立即打印物流面单" v-model="settingData.isPrint" />
      </el-form-item>

      <el-form-item label="" prop="">
        <el-checkbox label="重复打印物流面单时提醒" v-model="settingData.isPrintRepeatTip" />
      </el-form-item>

      <el-form-item label="" prop="">
        <el-checkbox label="复核后立即称重" v-model="settingData.toWeight" @change="weightCheck" />
      </el-form-item>
      <el-form-item label="" prop="">
        <el-radio-group :disabled="!settingData.toWeight" v-model="settingData.weighType">
          <el-radio value="1">手动称重</el-radio>
          <el-radio value="2">自动计算称重</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="" prop="" v-if="settingData.toWeight">
        <el-checkbox label="称重之后立即出库" v-model="settingData.outOfStock" />
      </el-form-item>

      <el-form-item label="" prop="">
        <el-checkbox label="扫描包材" v-model="settingData.scanPack" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="submitForm"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
