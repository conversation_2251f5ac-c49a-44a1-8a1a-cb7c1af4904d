<script setup>
import { ref, onMounted, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import {
  reviewScan,
  reviewMoreConsumer,
  reviewMorePiece,
  queryIntercepting,
  getPrinter,
  setPrinter,
} from '@/api/outbound/review'
import ScanArea from './components/ScanArea.vue'
import OrderList from './components/OrderList.vue'
import SettingDialog from './components/SettingDialog.vue'
import PrintDialog from './components/PrintDialog.vue'
import QueryDialog from './components/QueryDialog.vue'

const router = useRouter()

// 表格数据
const orderList = ref([])
const orderData = ref([])
const orderNoList = ref([])
const moreConsumer = ref([])
const morePiece = ref([])

// 表格加载状态
const loading = ref(false)

const barcodeDisabled = ref(true)
const moreOrderNoDisabled = ref(false)
const moreBarcodeDisabled = ref(true)
const moreNumDisabled = ref(true)
const moreBtnFinDisabled = ref(true)

const activeName = ref('single')
const orderListTips = ref('请扫描波次单号开始分拣')

const settingDialog = reactive({
  visible: false,
  title: '复核设置',
})

const printDialog = reactive({
  visible: false,
  title: '',
})
const queryDialog = reactive({
  visible: false,
  title: '',
})

const settingData = ref({
  isPrint: false, //复核后立即打印物流面单
  isPrintRepeatTip: false, //重复打印物流面单提醒
  toWeight: false, //复核后立即称重
  weighType: '', //称重方式
  outOfStock: '', //称重后立即出库
  scanPack: false, //扫描包材
})

const printData = ref([])

// 获取订单列表
const getList = async (orderNo) => {
  loading.value = true

  try {
    if (orderNoList.value.includes(orderNo)) {
      return
    }

    const params = {
      waveNo: orderNo,
      finishReview: false,
    }

    const res = await reviewScan(params)
    if (res.code === 200 && res.data) {
      orderNoList.value.push(orderNo)
      orderList.value = res.data || []
      orderData.value = orderData.value.concat(orderList.value)
      barcodeDisabled.value = false

      if (settingData.value.isPrintRepeatTip && orderList.value[0].expressPrintStatus == '20') {
        const printRow = {
          sourceNo: orderNo,
          printStatusName: '已打印',
        }
        printData.value.push(printRow)

        printDialog.visible = true
      }
    } else {
      orderList.value = []
      ElMessage.error(res.message || '获取数据失败')
    }
  } catch (error) {
    console.error('获取数据失败', error)
  } finally {
    loading.value = false
  }
}

// 获取 consumer 列表
const getMoreConsumer = async (orderNo) => {
  try {
    const params = {
      deliveryNo: orderNo,
    }

    const res = await reviewMoreConsumer(params)
    if (res.code === 200 && res.data.outBoundList) {
      orderList.value = []
      orderData.value = []

      moreConsumer.value = res.data || []

      getMorePiece(
        res.data.outBoundList[0].deliveryNo,
        res.data.outBoundList[0].customerCode,
        res.data.whCode,
      )
    } else {
      moreConsumer.value = []
      ElMessage.error(res.message || '获取数据失败')
    }
  } catch (error) {
    console.error('获取数据失败', error)
  } finally {
  }
}

// 获取 piece 列表
const getMorePiece = async (deliveryNo, customerCode, whCode) => {
  try {
    const params = {
      deliveryNo: deliveryNo,
      customerCode: customerCode,
      finishReview: false,
      whCode: whCode,
    }

    const res = await reviewMorePiece(params)
    if (res.code === 200 && res.data) {
      morePiece.value = res.data || []
      moreOrderNoDisabled.value = true
      moreBarcodeDisabled.value = false
    } else {
      moreBarcodeDisabled.value = true
      moreConsumer.value = []
      morePiece.value = []

      ElMessage.error(res.message || '获取数据失败')
    }
  } catch (error) {
    console.error('获取数据失败', error)
  } finally {
  }
}

const queryTips = ref(null)
const queryOrderNo = ref(null)
const queryOrderType = ref(1)
const checkQuery = async (orderNo, type) => {
  loading.value = true
  try {
    const params = {
      orderNo: orderNo,
    }
    const res = await queryIntercepting(params)
    if (res.code === 200) {
      if (res.data == 1) {
        // queryTips.value =
        queryOrderNo.value = orderNo
        queryOrderType.value = type
        queryDialog.visible = true
      } else {
        if (type == 1) {
          //一单一件
          getList(orderNo)
        } else if (type == 2) {
          //一单多件
          getMoreConsumer(orderNo)
        }
      }
    } else {
      orderList.value = []
      ElMessage.error(res.message || '获取数据失败')
    }
  } catch (error) {
    console.error('获取数据失败', error)
  } finally {
    loading.value = false
  }
}

//切换Tabs
const handleTabClick = (tabName) => {
  if (tabName == 'single') {
    orderListTips.value = '请扫描波次单号开始分拣'
  } else if (tabName == 'more') {
    orderListTips.value = '请扫描出库单号/物流跟踪号/格子号开始分拣'
  }
}

const handleScan = (orderNo) => {
  checkQuery(orderNo, 1)

  // getList(orderNo)
}

const handleMoreScan = (moreOrderNo) => {
  checkQuery(moreOrderNo, 2)

  // getList(orderNo)
}

const handleSetting = () => {
  settingDialog.visible = true
}

// 设置加载状态
const setLoading = (isLoading) => {
  loading.value = isLoading
}

const settingCancel = () => {
  settingDialog.visible = false
}

const printCancel = () => {
  printData.value = []

  printDialog.visible = false
}

const settingSuccess = () => {
  settingDialog.visible = false

  setUserPrinter()
}

const printSuccess = () => {
  printData.value = []
  printDialog.visible = false
}

const queryCancel = () => {
  queryDialog.visible = false
}

const queryIntercept = () => {
  queryDialog.visible = false

  router.push('/outbound/intercept/listing')
}

const querySuccess = () => {
  queryDialog.visible = false

  if (queryOrderType.value == 1) {
    //一单一件
    getList(queryOrderNo.value)
  } else if (queryOrderType.value == 2) {
    //一单多件
    getMoreConsumer(queryOrderNo.value)
  }
}

//获取用户打印设置
const getUserPrinter = async () => {
  try {
    const res = await getPrinter()
    if (res.code === 200 && res.data) {
      settingData.value.isPrint = res.data.isPrint
      settingData.value.isPrintRepeatTip = res.data.isPrintRepeatTip
      settingData.value.toWeight = res.data.toWeight

      settingData.value.weighType = ''
      if (res.data.weighType === true) {
        settingData.value.weighType = '1'
      } else if (res.data.weighType) {
        settingData.value.weighType = res.data.weighType
      }
      settingData.value.scanPack = res.data.scanPack
      settingData.value.outOfStock = res.data.outOfStock
    } else {
      ElMessage.error(res.message || '获取数据失败')
    }
  } catch (error) {
    console.error('获取数据失败', error)
  }
}

//用户打印设置
const setUserPrinter = async () => {
  try {
    const params = {
      isPrint: settingData.value.isPrint,
      isPrintRepeatTip: settingData.value.isPrintRepeatTip,
      toWeight: settingData.value.toWeight,
      weighType: settingData.value.weighType,
      scanPack: settingData.value.scanPack,
      outOfStock: settingData.value.outOfStock,
    }

    await setPrinter(params)
  } catch (error) {
    console.error('获取数据失败', error)
  }
}

// 初始化
onMounted(() => {
  getUserPrinter()
})
</script>

<template>
  <div class="swap-container">
    <el-row :gutter="20">
      <!-- 左侧扫描区域 -->
      <el-col :span="6">
        <ScanArea
          :loading="loading"
          :barcodeDisabled="barcodeDisabled"
          :moreOrderNoDisabled="moreOrderNoDisabled"
          :moreBarcodeDisabled="moreBarcodeDisabled"
          :activeName="activeName"
          :moreNumDisabled="moreNumDisabled"
          :moreBtnFinDisabled="moreBtnFinDisabled"
          @scan="handleScan"
          @more-scan="handleMoreScan"
          @setting="handleSetting"
          @tab-click="handleTabClick"
        />
      </el-col>

      <!-- 右侧订单区域 -->
      <el-col :span="18">
        <OrderList
          :loading="loading"
          :orderList="orderList"
          :orderData="orderData"
          :orderListTips="orderListTips"
          :moreConsumer="moreConsumer"
          :morePiece="morePiece"
          @loading-change="setLoading"
        />
      </el-col>
    </el-row>

    <!-- Setting -->
    <SettingDialog
      :visible="settingDialog.visible"
      :title="settingDialog.title"
      :settingData="settingData"
      @cancel="settingCancel"
      @success="settingSuccess"
    />

    <!-- 重复打印 -->
    <PrintDialog
      :visible="printDialog.visible"
      :printData="printData"
      @cancel="printCancel"
      @success="printSuccess"
    />

    <!-- QueryDialog -->
    <QueryDialog
      :visible="queryDialog.visible"
      :queryTips:="queryTips"
      @cancel="queryCancel"
      @success="querySuccess"
      @intercept="queryIntercept"
    />
  </div>
</template>

<style scoped>
.swap-container {
  padding: 20px;
  height: 100%;
}
</style>
