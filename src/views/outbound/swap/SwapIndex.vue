<script setup>
import { ref, onMounted, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { swapScan, getPrinter, setPrinter } from '@/api/outbound/swap'
import ScanArea from './components/ScanArea.vue'
import OrderList from './components/OrderList.vue'
import SettingDialog from './components/SettingDialog.vue'
import PrintDialog from './components/PrintDialog.vue'

// 表格数据
const orderList = ref([])
const orderData = ref([])
const orderNoList = ref([])

// 表格加载状态
const loading = ref(false)

const settingDialog = reactive({
  visible: false,
  title: '换单设置',
})

const printDialog = reactive({
  visible: false,
  title: '',
})

const settingData = ref({
  isOutbound: false,
  isPrintRepeatTip: false,
})

const printData = ref([])

// 获取订单列表
const getList = async (orderNo) => {
  loading.value = true

  try {
    if (orderNoList.value.includes(orderNo)) {
      return
    }

    const params = {
      deliveryNo: orderNo,
    }

    const res = await swapScan(params)
    if (res.code === 200 && res.data) {
      orderNoList.value.push(orderNo)
      orderList.value = res.data || []
      orderData.value = orderData.value.concat(orderList.value)

      if (settingData.value.isPrintRepeatTip && orderList.value[0].expressPrintStatus == '20') {
        const printRow = {
          sourceNo: orderNo,
          printStatusName: '已打印',
        }
        printData.value.push(printRow)

        printDialog.visible = true
      }
    } else {
      orderList.value = []
      ElMessage.error(res.message || '获取数据失败')
    }
  } catch (error) {
    console.error('获取数据失败', error)
    //ElMessage.error('获取数据失败')
    //orderList.value = []
  } finally {
    loading.value = false
  }
}

const handleScan = (orderNo) => {
  getList(orderNo)
}

const handleSetting = () => {
  settingDialog.visible = true
}

// 打印面单
const handlePrintOrder = (row) => {
  if (row.expressPrintStatus == '20' && settingData.value.isPrintRepeatTip) {
    printData.value = []

    const printRow = {
      sourceNo: row.sourceNo,
      printStatusName: '已打印',
    }
    printData.value.push(printRow)

    printDialog.visible = true
  } else {
    ElMessage.success('todo print - ' + row.sourceNo)
  }
}

// 设置加载状态
const setLoading = (isLoading) => {
  loading.value = isLoading
}

const settingCancel = () => {
  settingDialog.visible = false
}

const printCancel = () => {
  printData.value = []

  printDialog.visible = false
}

const settingSuccess = () => {
  settingDialog.visible = false

  setUserPrinter()
}

const printSuccess = () => {
  ElMessage.success('todo print - ' + printData.value[0].sourceNo)
  printData.value = []
  printDialog.visible = false
}

//获取用户打印设置
const getUserPrinter = async () => {
  try {
    const res = await getPrinter()
    if (res.code === 200 && res.data) {
      settingData.value.isOutbound = res.data.isOutbound
      settingData.value.isPrintRepeatTip = res.data.isPrintRepeatTip
    } else {
      ElMessage.error(res.message || '获取数据失败')
    }
  } catch (error) {
    console.error('获取数据失败', error)
  }
}

//用户打印设置
const setUserPrinter = async () => {
  try {
    const params = {
      isOutbound: settingData.value.isOutbound,
      isPrintRepeatTip: settingData.value.isPrintRepeatTip,
    }

    await setPrinter(params)
  } catch (error) {
    console.error('获取数据失败', error)
  }
}

// 初始化
onMounted(() => {
  getUserPrinter()
})
</script>

<template>
  <div class="swap-container">
    <el-row :gutter="20">
      <!-- 左侧扫描区域 -->
      <el-col :span="6">
        <ScanArea :loading="loading" @scan="handleScan" @setting="handleSetting" />
      </el-col>

      <!-- 右侧订单区域 -->
      <el-col :span="18">
        <OrderList
          ref="permissionConfigRef"
          :loading="loading"
          :orderList="orderList"
          :orderData="orderData"
          @loading-change="setLoading"
          @print="handlePrintOrder"
        />
      </el-col>
    </el-row>

    <!-- Setting -->
    <SettingDialog
      :visible="settingDialog.visible"
      :title="settingDialog.title"
      :settingData="settingData"
      @cancel="settingCancel"
      @success="settingSuccess"
    />

    <!-- 重复打印 -->
    <PrintDialog
      :visible="printDialog.visible"
      :printData="printData"
      @cancel="printCancel"
      @success="printSuccess"
    />
  </div>
</template>

<style scoped>
.swap-container {
  padding: 20px;
  height: 100%;
}
</style>
