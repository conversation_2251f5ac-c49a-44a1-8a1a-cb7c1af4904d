<script setup>
import { defineProps, defineEmits, reactive, ref } from 'vue'
import { SuccessFilled } from '@element-plus/icons-vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  waveNo: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['success', 'cancel', 'down-wave'])

// 打印汇总拣货单
const printAllOrder = () => {
  emit('down-wave', 1, props.waveNo)
}

// 打印分拣拣货单
const printWhOrder = () => {
  emit('down-wave', 2, props.waveNo)
}

// 关闭对话框
const handleClose = () => {
  emit('cancel')
}

// 确定
const submitForm = async () => {
  try {
    emit('success', props.waveNo)
  } catch (error) {
    console.error('操作失败', error)
  }
}
</script>

<template>
  <el-dialog :model-value="visible" width="500px">
    <br />

    <div class="app-container">
      <div style="font-size: 20px; text-align: center">
        <span style="font-weight: bolder; color: black">
          <el-icon><SuccessFilled /></el-icon>
          成功
        </span>
        <br />
        <span style="font-size: 14px"
          >波次{{ props.waveNo }}生成成功，<br />
          打印<el-link
            style="margin-left: 5px; margin-right: 5px"
            type="primary"
            @click="printAllOrder"
          >
            汇总拣货单 </el-link
          >或<el-link style="margin-left: 5px" type="primary" @click="printWhOrder">
            分拣拣货单
          </el-link></span
        >
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="submitForm"> 查看此波次 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
