<script setup>
import { defineProps, defineEmits, reactive, ref } from 'vue'
import { Warning } from '@element-plus/icons-vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: '',
  },
  outOrderCount: {
    type: Number,
    default: 0,
  },
  outOrderData: {
    type: Object,
    default: () => ({}),
  },
})

const emit = defineEmits(['success', 'cancel'])

// 关闭对话框
const handleClose = () => {
  emit('cancel')
}

// 确定
const submitForm = async () => {
  try {
    emit('success')
  } catch (error) {
    console.error('操作失败', error)
  }
}
</script>

<template>
  <el-dialog :model-value="visible" :title="props.title" style="width: 450px">
    <el-divider />
    <div class="app-container" style="font-size: 15px">
      <div style="margin-left: 20px">
        <span>已选订单数量:</span><br />
        <span>
          <el-input
            v-model="props.outOrderCount"
            style="width: 240px; margin-top: 8px"
            disabled
          ></el-input>
        </span>
        <br />
        <span> 1.勾选出库单确认发货后，出库单状态变更为【已出库】</span>
        <br />
        <span>2.出库单的包裹重量为出库单内产品重量之和</span>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="submitForm"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
