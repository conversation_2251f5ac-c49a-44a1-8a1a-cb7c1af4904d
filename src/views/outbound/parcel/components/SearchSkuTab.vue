<script setup>
import { Search } from '@element-plus/icons-vue'
import { ref, defineProps, defineEmits, reactive, watch, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { skus } from '@/api/outbound/parcel'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: '',
  },
  loading: {
    type: Boolean,
    default: false,
  },
  selSkusEmp: {
    type: Boolean,
    default: false,
  },
  total: {
    type: Number,
    default: 0,
  },
  currentPage: {
    type: Number,
    default: 1,
  },
  pageSize: {
    type: Number,
    default: 20,
  },
})

const emit = defineEmits([
  'update:visible',
  'submitTab',
  'cancel',
  'selection-change',
  'pagination-change',
  'check-sku',
])

// SKU 列表
const skuList = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)

const drawer = ref(false)

const pageSelRows = ref([])
const selRowsTab = ref([])

// 查询条件
const queryParams = reactive({
  searchValue: '', // 搜索值
})

function searchSku() {
  fetchSkuList()
}

const multipleTableRef = ref()
const pageRows = ref([])

// 获取 SKU 列表
const fetchSkuList = async () => {
  props.loading = true
  try {
    if (props.selSkusEmp) {
      pageSelRows.value = []
      selRowsTab.value = []
    }

    const params = {
      current: currentPage.value,
      size: pageSize.value,
    }

    // 根据搜索类型和值拼接参数
    if (queryParams.searchValue) {
      params['sku'] = queryParams.searchValue
    }

    const response = await skus(params)
    if (response && response.data) {
      skuList.value = response.data.records || []

      pageRows.value = pageSelRows.value[response.data.current]

      if (pageRows.value) {
        nextTick(() => {
          skuList.value.forEach((sku) => {
            let result = pageRows.value.find((row) => row.skuQtyStr == sku.skuQtyStr)
            if (result) {
              multipleTableRef.value.toggleRowSelection(sku, true)
            }
          })
        })
      }

      total.value = response.data.total || 0
      currentPage.value = response.data.current || 1
      pageSize.value = response.data.size || 20
    }
  } catch (error) {
    console.error('获取SKU列表失败', error)
    ElMessage.error('获取SKU列表失败')
  } finally {
    props.loading = false
  }
}

const selectedRows = ref([]) // 表格选中行
const handleSelectionChange = (selection) => {
  selRowsTab.value = []
  selection.forEach((row) => {
    selRowsTab.value.push(row)
  })
  pageSelRows.value.map((row, page) => {
    if (page !== currentPage.value) {
      row.forEach((r) => {
        // if(!selRowsTab.value.includes(r.skuQtyStr)){
        selRowsTab.value.push(r)
        // }
      })
    }
  })

  if (selection) {
    selectSku()
  }

  selectedRows.value = selection
  emit('selection-change', selection)
}

const selectSku = () => {
  emit('check-sku')
}

// 处理页码或每页条数变化
const handleSizeChange = (size) => {
  pageSize.value = size
  fetchSkuList()
}

const handleCurrentChange = (page) => {
  pageSelRows.value[currentPage.value] = []
  selectedRows.value.forEach((row) => {
    pageSelRows.value[currentPage.value].push(row)
  })

  if (selectedRows.value) {
    selectSku()
  }

  currentPage.value = page
  fetchSkuList()
}

// 监听对话框显示状态
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      fetchSkuList()
    }
  },
)

const closeDialog = () => {
  emit('update:visible', false)
}

const selectedAllRows = ref([]) // 表格选中行(每页)
const submitTab = async () => {
  try {
    pageSelRows.value[currentPage.value] = []
    selectedRows.value.forEach((row) => {
      pageSelRows.value[currentPage.value].push(row)
    })

    if (selectedRows.value) {
      selectSku()
    }

    selectedAllRows.value = []
    pageSelRows.value.map((row, page) => {
      selectedAllRows.value = selectedAllRows.value.concat(row)
    })

    emit('submitTab', selectedAllRows)
  } catch (error) {
    console.error('操作失败', error)
  }
}

// 初始化
onMounted(() => {
  if (props.visible) {
    fetchSkuList()
  }
})
</script>

<template>
  <el-dialog
    :title="title"
    :model-value="visible"
    @update:model-value="(val) => emit('update:visible', val)"
    width="700px"
    append-to-body
    destroy-on-close
    :close-on-click-modal="false"
  >
    <!-- <el-form :model="queryParams" inline> -->
    <el-form-item prop="searchValue" class="search-item">
      <el-input
        v-model="queryParams.searchValue"
        placeholder="搜索SKU全称"
        clearable
        style="width: 250px"
        class="input-with-select"
        @keyup.enter="searchSku"
        :suffix-icon="Search"
      >
      </el-input>
    </el-form-item>

    <!-- </el-form> -->

    <el-table
      ref="multipleTableRef"
      height="500px"
      v-loading="loading"
      border="true"
      :data="skuList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="50" />
      <el-table-column label="SKU*数量" prop="skuQtyStr" :show-overflow-tooltip="true">
      </el-table-column>
      <el-table-column label="待处理出货单" prop="pendingOrderQty" :show-overflow-tooltip="true" />
    </el-table>

    <div class="pagination-container">
      <el-pagination
        background
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[10, 20, 50, 100]"
        :current-page="currentPage"
        :page-size="pageSize"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <template #footer>
      <div style="float: left">
        <el-button @click="drawer = true"
          >已选择 <label style="color: blue">{{ selRowsTab.length }}</label> 个产品</el-button
        >
        <el-drawer v-model="drawer" title="已选择的 SKU" :with-header="false">
          <el-table :data="selRowsTab" border="true">
            <el-table-column width="300" property="skuQtyStr" label="SKU * 数量" />
            <el-table-column width="200" property="pendingOrderQty" label="待处理出货单" />
          </el-table>
        </el-drawer>
      </div>

      <div class="dialog-footer">
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" @click="submitTab" :loading="loading"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
.search-item {
  margin-right: 8px;
}
</style>
