<script setup>
import { defineProps, defineEmits, reactive, ref } from 'vue'
import { Warning } from '@element-plus/icons-vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  whUserList: {
    type: Object,
    default: () => ({}),
  },
  waveSort: {
    type: Number,
    default: 0,
  },
  createWaveCount: {
    type: Number,
    default: 0,
  },
})

const emit = defineEmits(['success', 'cancel'])

const queryParams = reactive({
  sort: 0,
  whUser: '',
})

const whUserFlag = ref(false)

// 关闭对话框
const handleClose = () => {
  emit('cancel')
}

// 确定
const submitForm = async () => {
  try {
    emit('success', queryParams.sort, queryParams.whUser)
  } catch (error) {
    console.error('操作失败', error)
  }
}
</script>

<template>
  <el-dialog :model-value="visible" width="500px">
    <br />

    <div class="app-container">
      <div style="font-size: 20px; text-align: center">
        <span style="font-weight: bolder; color: black">
          <el-icon><Warning /></el-icon>
          生成波次？
        </span>
        <br />
        <span style="font-size: 14px"
          >已选择{{ props.createWaveCount }}个出库单，确认生成波次？</span
        >
      </div>
      <div style="margin-top: 20px; margin-left: 30px">
        <span>排序规则:</span><br />
        <span>
          <el-select v-model="queryParams.sort" style="width: 200px; margin-top: 10px">
            <el-option
              v-for="dict in waveSort"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            >
            </el-option>
          </el-select>
        </span>
        <br />
        <span>
          <el-checkbox style="padding-top: 20px" v-model="whUserFlag"> 分配拣货员 </el-checkbox>
        </span>
        <br />
        <span v-if="whUserFlag">
          <el-select v-model="queryParams.whUser" style="width: 200px; margin-top: 10px">
            <el-option v-for="dict in whUserList" :key="dict" :label="dict" :value="dict">
            </el-option>
          </el-select>
        </span>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="submitForm"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
