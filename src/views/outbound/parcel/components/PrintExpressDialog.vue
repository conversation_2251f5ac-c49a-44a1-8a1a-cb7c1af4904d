<script setup>
import { defineProps, defineEmits } from 'vue'
import { Warning } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  printData: {
    type: Object,
    default: () => ({}),
  },
  rePrintExpressCount: {
    type: Number,
    default: 0,
  },
})

const emit = defineEmits(['success', 'cancel', 'allPrint'])

// 关闭对话框
const handleClose = () => {
  emit('cancel')
}

// 打印未打印
const submitForm = async () => {
  try {
    if (props.rePrintExpressCount == props.printData.length) {
      ElMessage.error('没有可打印的单据')

      return
    }

    emit('success')
  } catch (error) {
    console.error('操作失败', error)
  }
}

// 打印全部
const allPrint = async () => {
  try {
    emit('allPrint')
  } catch (error) {
    console.error('操作失败', error)
  }
}
</script>

<template>
  <el-dialog :model-value="visible" width="500px">
    <br />

    <div class="app-container">
      <div style="font-size: 18px; text-align: center">
        <span style="font-weight: bolder; color: black">
          <el-icon><Warning /></el-icon>
          确认打印吗？
        </span>
        <br />
        <span style="font-size: 12px"
          >共选择{{ props.printData.length }}个出库单，
          {{ props.rePrintExpressCount }}个出库单面单重复打印</span
        >
      </div>

      <el-table :data="printData" style="width: 100%; margin-top: 40px">
        <el-table-column label="出库单号" prop="sourceNo" />
        <el-table-column label="面单打印状态" prop="expressPrintStatusName" align="center" />
      </el-table>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button @click="allPrint"> 打印全部单据 </el-button>
        <el-button type="primary" @click="submitForm"> 打印未打印单据 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
