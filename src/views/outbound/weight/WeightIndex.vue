<script setup>
import { ref, onMounted, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { weightScan, getPrinter, setPrinter, toWeight } from '@/api/outbound/weight'
import ScanArea from './components/ScanArea.vue'
import OrderList from './components/OrderList.vue'
import SettingDialog from './components/SettingDialog.vue'

// 表格数据
const orderList = ref([])
const orderData = ref([])
const orderNoList = ref([])

// 表格加载状态
const loading = ref(false)
const weightDisabled = ref(true)
const weightFinishDisabled = ref(true)

const settingDialog = reactive({
  visible: false,
  title: '称重设置',
})

const settingData = ref({
  toSize: false, //测量包裹尺寸
  scanPack: false, //扫描包材
  outOfStock: false, //称重后立即出库
})

// 获取订单列表
const getList = async (orderNo) => {
  loading.value = true

  try {
    if (orderNoList.value.includes(orderNo)) {
      return
    }

    const params = {
      deliveryNo: orderNo,
    }

    const res = await weightScan(params)
    if (res.code === 200 && res.data) {
      orderList.value = [res.data.delivery]
      orderData.value = res.data || []
      weightDisabled.value = false
      weightFinishDisabled.value = false

      console.log(orderList)
    } else {
      orderList.value = []
      ElMessage.error(res.message || '获取数据失败')
    }
  } catch (error) {
    console.error('获取数据失败', error)
  } finally {
    loading.value = false
  }
}

const handleScan = (orderNo) => {
  getList(orderNo)
}

const handleSetting = () => {
  settingDialog.visible = true
}

// 设置加载状态
const setLoading = (isLoading) => {
  loading.value = isLoading
}

const settingCancel = () => {
  settingDialog.visible = false
}

const settingSuccess = () => {
  settingDialog.visible = false

  setUserPrinter()
}

const handleFinish = (orderNo, weightVal) => {
  alert(orderNo)
  alert(weightVal)

  finishWeight(orderNo, weightVal)
}

//完成称重
const finishWeight = async (orderNo, weightVal) => {
  try {
    const params = {
      deliveryNo: orderNo,
      weightVal: weightVal,
      whCode: orderData.value.whCode,
    }

    const res = await toWeight(params)
    if (res.code === 200) {
      ElMessage.success('操作成功')
    } else {
      ElMessage.error(res.message || '操作失败')
    }
  } catch (error) {
    console.error('操作失败', error)
  }
}

//获取用户打印设置
const getUserPrinter = async () => {
  try {
    const res = await getPrinter()
    if (res.code === 200 && res.data) {
      settingData.value.toSize = res.data.toSize
      settingData.value.scanPack = res.data.scanPack
      settingData.value.outOfStock = res.data.outOfStock
    } else {
      ElMessage.error(res.message || '获取数据失败')
    }
  } catch (error) {
    console.error('获取数据失败', error)
  }
}

//用户打印设置
const setUserPrinter = async () => {
  try {
    const params = {
      toSize: settingData.value.toSize,
      scanPack: settingData.value.scanPack,
      outOfStock: settingData.value.outOfStock,
    }

    await setPrinter(params)
  } catch (error) {
    console.error('获取数据失败', error)
  }
}

// 初始化
onMounted(() => {
  getUserPrinter()
})
</script>

<template>
  <div class="swap-container">
    <el-row :gutter="20">
      <!-- 左侧扫描区域 -->
      <el-col :span="6">
        <ScanArea
          :loading="loading"
          :weightFinishDisabled="weightFinishDisabled"
          :weightDisabled="weightDisabled"
          @scan="handleScan"
          @setting="handleSetting"
          @weight-finish="handleFinish"
        />
      </el-col>

      <!-- 右侧订单区域 -->
      <el-col :span="18">
        <OrderList
          ref="permissionConfigRef"
          :loading="loading"
          :orderList="orderList"
          :orderData="orderData"
          @loading-change="setLoading"
        />
      </el-col>
    </el-row>

    <!-- Setting -->
    <SettingDialog
      :visible="settingDialog.visible"
      :title="settingDialog.title"
      :settingData="settingData"
      @cancel="settingCancel"
      @success="settingSuccess"
    />
  </div>
</template>

<style scoped>
.swap-container {
  padding: 20px;
  height: 100%;
}
</style>
