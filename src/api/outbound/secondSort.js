import request from '@/utils/request'

/**
 * 二次分拣 - 扫描单号
 * @param {Object} data - 查询参数
 * @returns {Promise} List
 */
export function secondSortScan(data) {
  return request({
    url: '/outbound/secondSort/scan',
    method: 'post',
    data,
  })
}

/**
 * 二次分拣 - 校验
 * @param {Object} data - 查询参数
 */
export function scanQuery(data) {
  return request({
    url: '/outbound/secondSort/scanQuery',
    method: 'post',
    data
  })
}

/**
 * 二次分拣 - 格子信息
 * @param {Object} data - 查询参数
 */
export function queryGridSku(data) {
  return request({
    url: '/outbound/secondSort/queryGridSku',
    method: 'post',
    data
  })
}

/**
 * 二次分拣 - 打印面单
 * @param {Object} data
 * @returns {Promise}
 */
export function batchDownloadExpress(data) {
  return request({
    url: '/outbound/secondSort/batchDownloadExpress',
    method: 'post',
    data,
    responseType: 'blob', // 指定响应类型为二进制数据
  })
}

/**
 * 二次分拣 - 获取用户打印设置
 */
export function getPrinter() {
  return request({
    url: '/outbound/secondSort/getPrinter',
    method: 'post',
  })
}

/**
 * 二次分拣 - 用户打印设置
 * @param {Object} data - 打印设置参数
 */
export function setPrinter(data) {
  return request({
    url: '/outbound/secondSort/setPrinter',
    method: 'post',
    data,
  })
}
