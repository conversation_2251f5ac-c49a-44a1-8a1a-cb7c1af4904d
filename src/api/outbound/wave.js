import request from '@/utils/request'

/**
 * 获取拣货波次列表
 * @param {Object} data - 查询参数
 * @returns {Promise} List
 */
export function getWaveList(data) {
  return request({
    url: '/outbound/batch/listing',
    method: 'post',
    data,
  })
}

/**
 * 获取波次状态统计
 * @returns {Promise} statusSum
 */
export function getWaveStatusSum(data) {
  return request({
    url: '/outbound/batch/statusSum',
    method: 'post',
    data,
  })
}

/**
 * 批量分配拣货员
 * @param {Object} data - 分配参数 {waveIds, pickerCode}
 * @returns {Promise}
 */
export function batchAssignPicker(data) {
  return new Promise((resolve) => {
    setTimeout(() => {
      const mockData = {
        code: 200,
        message: '批量分配成功',
        data: null,
      }
      resolve(mockData)
    }, 500)
  })
}

/**
 * 批量开始拣货
 * @param {Object} data - 波次ID列表 {waveIds}
 * @returns {Promise}
 */
export function batchStartPicking(data) {
  return new Promise((resolve) => {
    setTimeout(() => {
      const mockData = {
        code: 200,
        message: '批量开始拣货成功',
        data: null,
      }
      resolve(mockData)
    }, 500)
  })
}

/**
 * 批量完成拣货
 * @param {Object} data - 波次ID列表 {waveIds}
 * @returns {Promise}
 */
export function batchFinishPicking(data) {
  return new Promise((resolve) => {
    setTimeout(() => {
      const mockData = {
        code: 200,
        message: '批量完成拣货成功',
        data: null,
      }
      resolve(mockData)
    }, 500)
  })
}

/**
 * 批量打印拣货单
 * @param {Object} data - 波次ID列表 {waveIds}
 * @returns {Promise}
 */
export function batchPrintPickingList(data) {
  return new Promise((resolve) => {
    setTimeout(() => {
      const mockData = {
        code: 200,
        message: '批量打印成功',
        data: null,
      }
      resolve(mockData)
    }, 500)
  })
}

/**
 * 导出波次数据
 * @param {Object} data - 导出参数
 * @returns {Promise}
 */
export function exportWaveData(data) {
  return new Promise((resolve) => {
    setTimeout(() => {
      const mockData = {
        code: 200,
        message: '导出成功',
        data: null,
      }
      resolve(mockData)
    }, 1000)
  })
}

/**
 * 获取波次详情
 * @param {Object} data
 * @returns {Promise}
 */
export function getWaveDetail(data) {
  return request({
    url: '/outbound/batch/detail',
    method: 'post',
    data,
  })
}

/**
 * 获取波次出库单列表
 * @param {Object} data
 * @returns {Promise}
 */
export function getWaveOrderList(data) {
  return request({
    url: '/outbound/batch/orderList',
    method: 'post',
    data,
  })
}

/**
 * 获取波次产品明细
 * @param {Object} data
 * @returns {Promise}
 */
export function getWaveSkuList(data) {
  return request({
    url: '/outbound/batch/skuList',
    method: 'post',
    data,
  })
}

/**
 * 获取波次操作日志
 * @param {Object} data
 * @returns {Promise}
 */
export function getWaveOperationLog(data) {
  return request({
    url: '/outbound/batch/logs',
    method: 'post',
    data,
  })
}

/**
 * 打印汇总拣货单
 * @param {Object} data
 * @returns {Promise}
 */
export function getWaveDownload(data) {
  return request({
    url: '/outbound/batch/download',
    method: 'post',
    data,
    responseType: 'blob', // 指定响应类型为二进制数据
  })
}

/**
 * 波次 - 面单拼接
 * @param {Object} data
 * @returns {Promise}
 */
export function clickMergeLogistics(data) {
  return request({
    url: '/outbound/batch/clickMergeLogistics',
    method: 'post',
    data,
  })
}

/**
 * 波次 - 查看面单
 * @param {Object} data
 * @returns {Promise}
 */
export function getPreviewAndDownLoadUrl(data) {
  return request({
    url: '/outbound/batch/getPreviewAndDownLoadUrl',
    method: 'post',
    data,
  })
}

/**
 * 波次 - 面单打印数据
 * @param {Object} data
 * @returns {Promise}
 */
export function listReprintOrder(data) {
  return request({
    url: '/outbound/batch/listReprintOrder',
    method: 'post',
    data,
  })
}
