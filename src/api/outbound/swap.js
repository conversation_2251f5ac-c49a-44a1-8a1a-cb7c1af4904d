import request from '@/utils/request'

/**
 * 换单 - 扫描单号
 * @param {Object} data - 查询参数
 * @returns {Promise} List
 */
export function swapScan(data) {
  return request({
    url: '/outbound/swap/scan',
    method: 'post',
    data,
  })
}

/**
 * 换单 - 获取用户打印设置
 */
export function getPrinter() {
  return request({
    url: '/outbound/swap/getPrinter',
    method: 'post',
  })
}

/**
 * 换单 - 用户打印设置
 * @param {Object} data - 打印设置参数
 */
export function setPrinter(data) {
  return request({
    url: '/outbound/swap/setPrinter',
    method: 'post',
    data,
  })
}
