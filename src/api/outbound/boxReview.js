import request from '@/utils/request'

/**
 * 包裹复核 - 扫描单号
 * @param {Object} data - 查询参数
 * @returns {Promise} List
 */
export function brScan(data) {
  return request({
    url: '/outbound/boxReview/scan',
    method: 'post',
    data,
  })
}

/**
 * 包裹复核 - 扫描 barcode
 * @param {Object} data - 查询参数
 * @returns {Promise} List
 */
export function bcScan(data) {
  return request({
    url: '/outbound/boxReview/scanSku',
    method: 'post',
    data,
  })
}

/**
 * 包裹复核 - 获取用户打印设置
 */
export function getPrinter() {
  return request({
    url: '/outbound/boxReview/getPrinter',
    method: 'post',
  })
}

/**
 * 包裹复核 - 用户打印设置
 * @param {Object} data - 打印设置参数
 */
export function setPrinter(data) {
  return request({
    url: '/outbound/boxReview/setPrinter',
    method: 'post',
    data,
  })
}
